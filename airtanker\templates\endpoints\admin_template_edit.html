{% extends "base.html" %}

{% block styles %}
  <link rel="stylesheet" href="{{ url_for('static', filename='css/extra.css') }}" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" />
  <style>
    .template-form-card {
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 30px;
      margin-bottom: 20px;
    }
    
    .form-section {
      margin-bottom: 25px;
    }
    
    .form-section:last-child {
      margin-bottom: 0;
    }
    
    .form-label {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      display: block;
    }
    
    .form-help {
      font-size: 0.875rem;
      color: #6c757d;
      margin-top: 5px;
    }
    
    .json-editor {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.875rem;
      line-height: 1.4;
    }
    
    .btn-group-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
    }
    
    .template-header {
      border-bottom: 2px solid #f0f0f0;
      margin-bottom: 30px;
      padding-bottom: 15px;
    }
    
    .error-message {
      color: #dc3545;
      font-size: 0.875rem;
      margin-top: 5px;
    }
    
    .json-preview {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 15px;
      margin-top: 10px;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .json-preview pre {
      margin: 0;
      font-size: 0.875rem;
      color: #333;
    }
  </style>
{% endblock %}

{% block scripts %}
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // JSON validation and preview
      const responseSchemaField = document.getElementById('response_schema');
      const jsonPreview = document.getElementById('json-preview');
      
      function validateAndPreviewJSON() {
        try {
          const jsonValue = responseSchemaField.value.trim();
          if (jsonValue) {
            const parsed = JSON.parse(jsonValue);
            const formatted = JSON.stringify(parsed, null, 2);
            jsonPreview.innerHTML = '<pre><code class="language-json">' + formatted + '</code></pre>';
            Prism.highlightElement(jsonPreview.querySelector('code'));
            jsonPreview.style.display = 'block';
          } else {
            jsonPreview.style.display = 'none';
          }
        } catch (e) {
          jsonPreview.innerHTML = '<div class="error-message">Invalid JSON: ' + e.message + '</div>';
          jsonPreview.style.display = 'block';
        }
      }
      
      if (responseSchemaField) {
        responseSchemaField.addEventListener('input', validateAndPreviewJSON);
        validateAndPreviewJSON(); // Initial validation
      }
      
      // Form validation
      const form = document.querySelector('form');
      if (form) {
        form.addEventListener('submit', function(e) {
          const responseSchema = document.getElementById('response_schema').value.trim();
          if (responseSchema) {
            try {
              JSON.parse(responseSchema);
            } catch (error) {
              e.preventDefault();
              alert('Please fix the JSON syntax in the Response Schema field before submitting.');
              return false;
            }
          }
        });
      }
    });
  </script>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="row">
    <div class="col-12">
      <div class="template-header">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h1>
              <i class="ri-file-code-line"></i> 
              {{ action }} Template
            </h1>
            <p class="text-muted">{{ action }} AI prompt template for data processing</p>
          </div>
          <div>
            <a href="{{ url_for('admin_templates') }}" class="btn btn-outline-secondary">
              <i class="ri-arrow-left-line"></i> Back to Templates
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Flash Messages -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row">
    <div class="col-12">
      <div class="template-form-card">
        <form method="POST" novalidate>
          {{ form.hidden_tag() }}
          
          <div class="form-section">
            {{ form.name.label(class="form-label") }}
            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
            {% if form.name.errors %}
              <div class="error-message">
                {% for error in form.name.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">A descriptive name for this template (e.g., "Customer Timesheet Parser")</div>
          </div>

          <div class="form-section">
            {{ form.type.label(class="form-label") }}
            {{ form.type(class="form-select" + (" is-invalid" if form.type.errors else "")) }}
            {% if form.type.errors %}
              <div class="error-message">
                {% for error in form.type.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">Category or type of data this template processes</div>
          </div>

          <div class="form-section">
            {{ form.identifiers.label(class="form-label") }}
            {{ form.identifiers(class="form-control" + (" is-invalid" if form.identifiers.errors else "")) }}
            {% if form.identifiers.errors %}
              <div class="error-message">
                {% for error in form.identifiers.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">Comma-separated keywords that help identify when to use this template</div>
          </div>

          <div class="form-section">
            {{ form.prompt.label(class="form-label") }}
            {{ form.prompt(class="form-control" + (" is-invalid" if form.prompt.errors else "")) }}
            {% if form.prompt.errors %}
              <div class="error-message">
                {% for error in form.prompt.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">The AI prompt that will be used to process data with this template</div>
          </div>

          <div class="form-section">
            {{ form.response_schema.label(class="form-label") }}
            {{ form.response_schema(class="form-control json-editor" + (" is-invalid" if form.response_schema.errors else ""), id="response_schema") }}
            {% if form.response_schema.errors %}
              <div class="error-message">
                {% for error in form.response_schema.errors %}{{ error }}{% endfor %}
              </div>
            {% endif %}
            <div class="form-help">JSON schema defining the expected structure of the AI response</div>
            <div id="json-preview" class="json-preview" style="display: none;"></div>
          </div>

          <div class="btn-group-actions">
            <a href="{{ url_for('admin_templates') }}" class="btn btn-secondary">Cancel</a>
            <button type="submit" class="btn btn-primary">
              <i class="ri-save-line"></i> {{ action }} Template
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}
