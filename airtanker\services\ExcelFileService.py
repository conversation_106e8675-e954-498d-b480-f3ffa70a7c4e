import csv
import re
from openpyxl import load_workbook
from itertools import tee, islice, chain
from services.NameParser import contains_emails
from services.FormatPicker import determine_format, FormatType
from sheet_templates.ATS import parse_ats_cambridge_short, parse_ats_cambridge_long, parse_ats_ohio
from sheet_templates.KUKA import parse_kuka_customer_sheet, parse_kuka
from sheet_templates.HTI import parse_hti_internal
from sheet_templates.Valiant import parse_valiant_data_from_pdf
from sheet_templates.HTI import parse_hti_data_from_pdf
from sheet_templates.Dominion import parse_dominion_data_from_pdf
from services.ContractorService import parse_contractors
from services.EmployeeService import parse_employees
from services.DatabaseService import DatabaseService
from services.PaycorService import parse_time_report
from services.NameParser import find_name_column_excel, find_name_column_csv, contains_emails, get_name_td
from services.DateParser import find_date_column_excel, find_date_column_in_csv, check_daterange
from services.Extra import to_float, to_str
from datetime import datetime
from models.TimesheetRecord import Timesheet_Weekly_Record
from nameparser import HumanName
import logging
from dotenv import load_dotenv
import os
from main import airtanker_app
from fuzzywuzzy import process
from datetime import datetime, date, timedelta
from services.OdooService import OdooService
from dateutil import parser
import pandas as pd
import camelot
from PyPDF2 import PdfReader

def parse_date(date_str):
    try:
        return parser.parse(date_str).date()
    except (ValueError, TypeError):
        return date_str
    

class ExcelFileService:

    timesheet_records = []
    error_logs = []
    name_errors = []


    def __init__(self):
        pass    

    # DONE with internal. Gives weekly reports (7 day max) for all employees in TD and Odoo.
    def parse_internal_files(self, files, selected_week_ending, odooService):
        '''Parses the internal files that are uploaded and adds the row data
        into the timesheet_records array using the Timesheet_Weekly_Record class.'''

        template_config = [
            { # Contractor Template
                "name": "Contractor",
                "source": "Internal",
                "signature": {"cell": "B1", "value": "EMPLOYEE TIMESHEET"},
                "parser_options": {
                    "header": 0,
                },
            },
            { # ADP/Odoo Template
                "name": "ADP",
                "source": "ADP",
                "signature": {"cell": "B1", "value": "Payroll Name"},
                "parser_options": {
                    "header": 0,
                }
            },
            {  # Paycor Template
                "name": "Paycor",
                "source": "Paycor",
                "signature": {  # How to identify this template
                    "cell": "B1",
                    "value": "First Name",
                },
                "parser_options": {  # How to parse it with pandas
                    "header": 0,  # Header is in the first row (index 0) real data starts on row 2 (index 1)
                    "names": [
                        "lastName",
                        "firstName",
                        "col_C", # Placeholder for skipped columns
                        "badgeNumber",
                        "date",
                        "col_F",
                        "col_G",
                        "customer",
                        "project",
                        "task",
                        "code",
                        "hours",
                    ],
                    "usecols": "A,B,D,E,H,I,J,K,L", # Use column letters
                },
            },
            { # Kuka Template
                "name": "Kuka",
                "source": "Internal",
                "signature": {"cell": "A1", "value": "Weekly Time Report"},
                "parser_options": {
                    "header": 0,
                },
            },
            {  # HTI Template
                "name": "HTI",
                "source": "Internal",
                "signature": {"cell": "A1", "value": "Employee Name:"},
                "parser_options": {
                    "header": 0,
                },
            },
        ]

        billable_codes = {'Reg', 'OT', 'DBT', 'TRT'}

        fileIds = []
        error_entry_id_counter = 1

        database_service = DatabaseService()
        database_service.connect()

        # Get the active work orders from the database
        all_active_work_order_entries = database_service.get_active_work_orders(selected_week_ending=selected_week_ending)

        for file in files:
            if not file or not file.filename.endswith(".xlsx"):
                continue # with next file

            try:
                workbook = load_workbook(filename=file, data_only=True)
            except Exception as e:
                # print(f"Could not load {file.filename}: {e}")
                airtanker_app.logger.error(f"Could not load {file.filename}: {e}")
                continue

            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]

                if sheet.sheet_state != "visible" or sheet_name in [
                    "Service Report",
                    "Instructions"
                ]:
                    continue

                for config in template_config:
                    signature = config["signature"]
                    if sheet[signature["cell"]].value == signature["value"]:
                        template = config
                        break
                    else:
                        template = None

                if not template:
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file.filename,
                        "Message":f"Couldnt find a valid template for sheet: '{sheet_name}' from the '{file.filename}' file. Is this an ADP / Contractor timesheet?",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{},
                        'Employee': '',
                        'EmployeeID':'',
                        'Hours': []
                    }
                    self.error_logs.append(error)
                    error_entry_id_counter += 1
                    airtanker_app.logger.error(f"Couldnt find a valid template for sheet: '{sheet_name}' from the '{file.filename}' file. Is this an ADP / Contractor timesheet?")
                    continue

                # Check if file and sheet have already been processed.
                file_id, file_already_processed = database_service.insert_filename_get_id(filename=file.filename, sheet_name=sheet_name, source_type=template["source"])
                
                # If file already processed, add error, skip the file.
                if file_already_processed:
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file.filename,
                        "Message":f"This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{
                        },
                        'Employee': '',
                        'EmployeeID':'',
                        'Hours': []
                    }
                    
                    self.error_logs.append(error)
                    error_entry_id_counter += 1
                    continue
                else:
                    # If not already processed, insert file ID. We use this for front end.
                    fileIds.append(file_id)

                # < -------------------- Begin parsing the file, once we have determined the template -------------------- >

                # If it's kuka internal sheet, parse_kuka()
                if template["name"] == "Kuka":
                    errors, name_errors, error_entry_id_counter = parse_kuka(sheet=sheet,
                                        sheet_name=sheet_name,
                                        file_name=file.filename,
                                        file_id=file_id,
                                        error_entry_id_counter=error_entry_id_counter,
                                        selected_week_ending=selected_week_ending,
                                        file=file,
                                        database_service=database_service,
                                        all_active_work_order_entries=all_active_work_order_entries)
                    continue_to_next_file = True

                # Skip HTI for now.
                # TODO: Modify this to work with HTI sheets.
                elif template["name"] == "HTI":
                    errors, name_errors, error_entry_id_counter = parse_hti_internal(sheet=sheet,
                                        sheet_name=sheet_name,
                                        file_name=file.filename,
                                        file_id=file_id,
                                        error_entry_id_counter=error_entry_id_counter,
                                        selected_week_ending=selected_week_ending,
                                        database_service=database_service,
                                        all_active_work_order_entries=all_active_work_order_entries)
                    continue_to_next_file = True

                # If it's contractor sheet, parse_contractors()
                elif template["name"] == "Contractor":
                    errors, name_errors, error_entry_id_counter = parse_contractors(sheet=sheet,
                                        file_id=file_id,
                                        file_name=file.filename,
                                        error_entry_id_counter=error_entry_id_counter,
                                        selected_week_ending=selected_week_ending,
                                        database_service=database_service,
                                        all_active_work_order_entries=all_active_work_order_entries)
                    continue_to_next_file = True
                # If it's odoo sheet (ADP currently), parse_employees()
                elif template["name"] == "ADP":
                    errors, name_errors, error_entry_id_counter = parse_employees(sheet=sheet,
                                            sheet_name=sheet_name,
                                            file_name=file.filename,
                                            file_id=file_id,
                                            error_entry_id_counter=error_entry_id_counter,
                                            selected_week_ending=selected_week_ending,
                                            odooService=odooService,
                                            database_service=database_service,
                                            all_active_work_order_entries=all_active_work_order_entries)
                    continue_to_next_file = True
                elif template["name"] == "Paycor":
                    ## The following lines are going to be generic for all templates, maybe wont need individial parsing functions, just the template config.
                    try:
                        df = pd.read_excel(file, sheet_name=sheet_name, **template["parser_options"])

                        # Add metadata
                        df['file_id'] = file_id
                        df['file_name'] = file.filename
                        df['sheet_name'] = sheet_name
                        df['source'] = template["source"]
                        df['week_ending'] = selected_week_ending
                        df['import_type'] = 'external'

                        # Convert hour and date types
                        df['hours'] = pd.to_numeric(df['hours'], errors='coerce').fillna(0)
                        df['date'] = pd.to_datetime(df['date'], errors='coerce')

                        ## Clean data, remove lines with 0 hours and no date
                        df = df[df['hours'] > 0]
                        df = df.dropna(subset=['date'])

                        ## Filter data
                        # Week relevant
                        week_ending = datetime.strptime(selected_week_ending, '%Y-%m-%d') # ToDo: Move this line out of the loop to not reload it every time, though, you should check no other placec is needed in string format.
                        df = df[df['date'] >= week_ending - timedelta(days=6)]
                        df = df[df['date'] <= week_ending]
                        # Ignore 'Administration' project
                        df = df[df['project'] != 'Administration']
                        # Just billable codes
                        df = df[df['code'].isin(billable_codes)]

                        # Save as Excel file and log directory

                    except Exception as e:
                        airtanker_app.logger.error(f"Error reading Excel file for Paycor: {e}")
                        # ToDo:
                        # On error, remove file and tell user to retry
                        # Or show user as an error 'remove file from edit files page and retry'
                        continue


                    timecards = []
                    for row in sheet.iter_rows(min_row=2):
                        employee_punch = {
                            'firstName': row[1].value, 
                            'lastName': row[0].value, 
                            'badgeNumber': to_str(row[3].value, 'Missing'),
                            'date': row[4].value, 
                            'customer': to_str(row[7].value), 
                            'project': to_str(row[8].value), 
                            'task': to_str(row[9].value), 
                            'code': row[10].value, 
                            'hours': to_float(row[11].value)}
                        timecards.append(employee_punch)

                    errors, name_errors, error_entry_id_counter = parse_time_report(timecards=timecards,
                                            import_type='external',
                                            file_name=file.filename,
                                            file_id=file_id,
                                            error_entry_id_counter=error_entry_id_counter,
                                            week_ending=selected_week_ending,
                                            odoo_service=odooService,
                                            database_service=database_service,
                                            active_wos=all_active_work_order_entries)
                    continue_to_next_file = True
                
                if errors is not None:
                    self.error_logs.extend(errors)

                if name_errors is not None:
                    self.name_errors.extend(name_errors)
                    
                # Don't process any other sheets if we already processed one. Move to next file.
                if continue_to_next_file:
                    # break out of looping through the sheets. Go to next file. We've got the data.
                    break
        database_service.disconnect()
        return self.error_logs, self.name_errors, fileIds
    
    def parse_fixed_files(self, files, selected_week_ending, odooService):
        '''
        Parses fixed-bid timecards that are uploaded from Paycor
        '''
        file_ids = []
        error_index = 1

        database_service = DatabaseService()
        database_service.connect()
        active_work_orders = database_service.get_active_work_orders(selected_week_ending=selected_week_ending)

        for file in files:
            if file:
                if file.filename.endswith('.xlsx'):
                    db_filename = file.filename + '-Internal WE' + selected_week_ending

                    workbook = load_workbook(filename=file, data_only=True)
                    sheet = workbook['AirTanker Timecard Report']
                    if sheet:
                        file_id, file_already_processed = database_service.insert_filename_get_id(filename=db_filename, sheet_name=sheet.title, source_type='Paycor')
                        if file_already_processed:
                            error = {
                                'ID': error_index,
                                'FileName': file.filename,
                                "Message":f"This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID':'',
                                'Hours': []
                            }
                            self.error_logs.append(error)
                            error_index += 1
                            continue
                        else:
                            file_ids.append(file_id)

                        timecards = []
                        for row in sheet.iter_rows(min_row=2):
                            employee_punch = {
                                    'firstName': row[1].value, 
                                    'lastName': row[0].value, 
                                    'badgeNumber': to_str(row[3].value, 'Missing'),
                                    'date': row[4].value, 
                                    'customer': to_str(row[7].value), 
                                    'project': to_str(row[8].value), 
                                    'task': to_str(row[9].value), 
                                    'code': row[10].value, 
                                    'hours': to_float(row[11].value)}
                            timecards.append(employee_punch)

                        errors, name_errors, error_index = parse_time_report(timecards=timecards,
                                                    import_type='internal',
                                                    file_name=file.filename,
                                                    file_id=file_id,
                                                    error_entry_id_counter=error_index,
                                                    week_ending=selected_week_ending,
                                                    odoo_service=odooService,
                                                    database_service=database_service,
                                                    active_wos=active_work_orders)
                        
                        if errors is not None:
                            self.error_logs.extend(errors)

                        if name_errors is not None:
                            self.name_errors.extend(name_errors)
                    else:
                        self.error_logs.append({
                                'ID': error_index,
                                'FileName': file.filename,
                                "Message":f"This file could not be parsed for Paycor. Sheet name was expected to be 'AirTanker Timecard Report' but was '{sheet.title}'",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID':'',
                                'Hours': []
                            })
                        error_index += 1
                        continue
                else:
                    airtanker_app.logger.error(f'Error parsing fixed-bid time reports: File {file.filename} is not an Excel XLSX format!')
            else:
                airtanker_app.logger.error('Error parsing fixed-bid time reports: No file to parse!')
        database_service.disconnect()
        return self.error_logs, self.name_errors, file_ids

    # For Customer Sheets. They're all excel currently.
    def parse_external_files(self, files, selected_week_ending):
        '''
        Parses the external files and looks up the person via name from the timesheet_records array.
        It adds the hours to the external hours part of the class and triggers error if needed. With notes.
        '''

        error_entry_id_counter = 1

        # Connect to the DB
        database_service = DatabaseService()
        database_service.connect()
        all_active_work_order_entries = database_service.get_active_work_orders(selected_week_ending=selected_week_ending)

        for file in files:
            if file:
                if file.filename.endswith('.xlsx'):
                    try:
                        airtanker_app.logger.debug(f"Beginning Parsing ATS worksheet: {file.filename}")
                        workbook = load_workbook(filename=file, data_only=True)  # Load the workbook                    

                        # iterate through each sheet in the workbook
                        sheet_count = 0
                        for sheet_name in workbook.sheetnames:
                            if sheet_name == "Service Report":
                                continue   
                            if sheet_name == "Instructions":
                                continue                       
                            sheet = workbook[sheet_name]
                            if sheet.sheet_state != 'visible':
                                continue
                            if contains_emails(sheet) or all(sheet.cell(row=row, column=col).value is None for row in range(1, 11) for col in range(1, 11)):
                                continue
                            sheet_count += 1
                            # check if it's a placeholder sheet, or empty sheet (first 10 rows, and 10 columns for empty)
                            # if contains_emails(sheet) or all(sheet.cell(row=row, column=col).value is None for row in range(1, 11) for col in range(1, 11)):
                            #     continue # go to next sheet if so
                            # el
                            if sheet_count > 1:
                                break
                            
                            sheet_format, error, file_id, kuka_type = determine_format(sheet=sheet,
                                                                            sheet_name=sheet_name,
                                                                            file_name=file.filename,
                                                                            error_entry_id_counter=error_entry_id_counter,
                                                                            database_service=database_service)
                            if error:
                                self.error_logs.append(error)
                                error_entry_id_counter += 1
                                error = None
                                continue

                            match sheet_format:
                                case FormatType.ATS_CAMBRIDGE_LONG:
                                    errors, name_errors, error_entry_id_counter = parse_ats_cambridge_long(sheet=sheet,
                                                                    sheet_name=sheet_name,
                                                                    file_name=file.filename,
                                                                    file_id=file_id,
                                                                    error_entry_id_counter=error_entry_id_counter,
                                                                    selected_week_ending=selected_week_ending,
                                                                    database_service=database_service,
                                                                    all_active_work_order_entries=all_active_work_order_entries)
                                case FormatType.ATS_CAMBRIDGE_SHORT:                                
                                    errors, name_errors, error_entry_id_counter = parse_ats_cambridge_short(sheet=sheet,
                                                                    sheet_name=sheet_name,
                                                                    file_name=file.filename,
                                                                    file_id=file_id,
                                                                    error_entry_id_counter=error_entry_id_counter,
                                                                    selected_week_ending=selected_week_ending,
                                                                    database_service=database_service,
                                                                    all_active_work_order_entries=all_active_work_order_entries)
                                case FormatType.ATS_OHIO:
                                    error = {
                                    'ID':error_entry_id_counter,
                                    'FileName': file.filename,
                                    "Message":f"ATS Ohio Green Sheets are unable to be processed at this time. Thank you for your patience.",
                                    'ReportedProjectNumber':'',
                                    'WorkOrderEntry':{
                                    },
                                    'Employee': '',
                                    'EmployeeID':'',
                                    'Hours': []
                                }
                                
                                    self.error_logs.append(error)
                                    error_entry_id_counter += 1
                                    database_service.delete_data_with_fileID(file_id)
                                    break
                                    errors, name_errors, error_entry_id_counter = parse_ats_ohio(sheet=sheet,
                                                            sheet_name=sheet_name,
                                                            file_name=file.filename,
                                                            file_id=file_id,
                                                            error_entry_id_counter=error_entry_id_counter,
                                                            selected_week_ending=selected_week_ending)
                                case FormatType.KUKA:                                
                                    errors, name_errors, error_entry_id_counter = parse_kuka_customer_sheet(sheet=sheet,
                                                            sheet_name=sheet_name,
                                                            file_name=file.filename,
                                                            file_id=file_id,
                                                            error_entry_id_counter=error_entry_id_counter,
                                                            selected_week_ending=selected_week_ending,
                                                            database_service=database_service,
                                                            all_active_work_order_entries=all_active_work_order_entries,
                                                            type=kuka_type)  
                                case default:
                                    error = {
                                        'ID':error_entry_id_counter,
                                        'FileName': file.filename,
                                        "Message":f"Couldn't determine format of customer file.",
                                        'ReportedProjectNumber':'',
                                        'WorkOrderEntry':{
                                        },
                                        'Employee': '',
                                        'EmployeeID':'',
                                        'Hours': []
                                    }
                                    
                                    self.error_logs.append(error)
                                    error_entry_id_counter += 1
                                    database_service.delete_data_with_fileID(file_id)
                                    continue

                            if errors is not None:
                                self.error_logs.extend(errors)

                            if name_errors is not None:
                                self.name_errors.extend(name_errors)
                    except Exception as e:
                        error_message = f'Error reading Excel Sheet: {e}'
                        error = {
                            'ID':error_entry_id_counter,
                            'FileName': file.filename,
                            "Message":error_message,
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{
                            },
                            'Employee': '',
                            'EmployeeID':'',
                            'Hours': []
                        }
                        database_service.disconnect() 
                        self.error_logs.append(error)
                        error_entry_id_counter += 1
                        continue

                elif file.filename.endswith('.pdf'):
                    # Save the uploaded file to a temporary location
                    temp_pdf_path = "temp_uploaded_file.pdf"
                    file.save(temp_pdf_path)

                    # Function that iterates over all the pdf pages to look for tables
                    # Returns the data as df (or json), the pdf_customer_format and an error message if its the case
                    df, pdf_customer_format, page_number, error_message = check_pdf_tables_and_format(temp_pdf_path)

                    if pdf_customer_format == 'Unknown' or error_message:
                        templates_in_progress = ["FLT", "Fori"]

                        if any(t.lower() in file.filename.lower() for t in templates_in_progress):
                            error_message += " - This template is in progress."
                        
                        # Throw error
                        error = {
                            'ID':error_entry_id_counter,
                            'FileName': file.filename,
                            "Message":error_message,
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{
                            },
                            'Employee': '',
                            'EmployeeID':'',
                            'Hours': [],
                        }

                        airtanker_app.logger.error(f"Error with {file.filename}: {error_message}")
                        
                        self.error_logs.append(error)
                        error_entry_id_counter += 1
                        # database_service.delete_data_with_fileID(file_id) # Commented because 'file_id' is not defined before this line
                        os.remove(temp_pdf_path)
                        continue
                    else:
                        # Check if file has been processed already
                        file_id, file_already_processed = database_service.insert_filename_get_id(filename=file.filename, sheet_name="PDF", source_type=pdf_customer_format)

                        # if file already processed, add error, skip the file.
                        if file_already_processed:
                            error = {
                                'ID':error_entry_id_counter,
                                'FileName': file.filename,
                                "Message":f"This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID':'',
                                'Hours': []
                            }
                            
                            self.error_logs.append(error)
                            error_entry_id_counter += 1
                            os.remove(temp_pdf_path)
                            continue



                        if pdf_customer_format == 'HTI':
                            # Parse HTI PDF
                            errors, name_errors, error_entry_id_counter = parse_hti_data_from_pdf(df=df,
                                                                                            error_entry_id_counter=error_entry_id_counter,
                                                                                            file_name=file.filename,
                                                                                            all_active_work_order_entries=all_active_work_order_entries,
                                                                                            file_id=file_id)
                        
                        elif pdf_customer_format == "Valiant":
                            # Parse Valiant PDF
                            # NEED to use lattice flavor for proper parsing of Valiant sheets

                            tables = camelot.read_pdf(temp_pdf_path, pages=page_number, multiple_tables=False)
                            df = tables[0].df
                            errors, name_errors, error_entry_id_counter = parse_valiant_data_from_pdf(df=df,
                                                                                            error_entry_id_counter=error_entry_id_counter,
                                                                                            file_name=file.filename,
                                                                                            all_active_work_order_entries=all_active_work_order_entries,
                                                                                            file_id=file_id)
                        
                        elif pdf_customer_format == "Dominion":
                            # Parse Dominion PDF
                            errors, name_errors, error_entry_id_counter = parse_dominion_data_from_pdf(df=df,
                                                                                            temp_pdf_path=temp_pdf_path,
                                                                                            error_entry_id_counter=error_entry_id_counter,
                                                                                            file_name=file.filename,
                                                                                            all_active_work_order_entries=all_active_work_order_entries,
                                                                                            file_id=file_id,
                                                                                            selected_week_ending=selected_week_ending)

                        if errors is not None:
                            self.error_logs.extend(errors)

                        if name_errors is not None:
                            self.name_errors.extend(name_errors)
                        
                        os.remove(temp_pdf_path)

        database_service.disconnect()        
        return self.error_logs, self.name_errors

    def parse_internal_expenses(self,
                                files,
                                selected_week_ending,
                                odooService # type: OdooService
                                ):
        error_entry_id_counter = 1
        fileIDs = []

        database_service = DatabaseService()
        database_service.connect()

        load_dotenv()

        # row, column
        contractor_idx = [4, 6]        
        week_ending_idx = [6, 9]        
        job_num = [4, 3]
        job_site = [5, 3]

        row_start = 11

        date_col = 2
        account_col = 3
        desc_col = 4
        hotel_col = 5
        transport_col = 6
        per_diem_col = 7
        phone_col = 8
        misc_col = 9
        mileage_col = 11

        all_active_work_order_entries = database_service.get_active_work_orders(selected_week_ending=selected_week_ending)
        
        # Extract just the names from all_employee_names for matching
        names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

        # Loop through the files
        for file in files:
            if file:
                # Switching to using Odoo and the Contractor Sheets. Not TD anymore.
                if not file.filename.endswith('.xlsx'):
                    continue
                
                workbook = load_workbook(filename=file, data_only=True)
                sheet = workbook.worksheets[0]
                source = "Expense"

                # Check format of the file
                format_indicator = str(sheet.cell(row=1, column=2).value).strip().upper()
                if format_indicator != "EXPENSE REPORT":
                        error = {
                            'ID':error_entry_id_counter,
                            'FileName': file.filename,
                            'FileID': None,
                            "Message":f"Couldnt find a valid template for sheet: '{sheet.title}' from the '{file.filename}' file. Is this an AtomTech Expense Report? We only support AtomTech Reports ATM.",
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{
                            },
                            'Employee': '',
                            'EmployeeID':'',
                            'Hours': []
                        }
                        
                        self.error_logs.append(error)
                        error_entry_id_counter += 1
                        continue

                # Check if the file has been processed
                file_id, file_already_processed = database_service.insert_filename_get_id(filename=file.filename, sheet_name=sheet.title, source_type=source)

                # if file already processed, skip it.
                if file_already_processed:
                    error = {
                        'ID':error_entry_id_counter,
                        'FileName': file.filename,
                        "FileID": None,
                        "Message":f"This file has already been processed. Delete the sheet at '/edit-files' to re-parse.",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{
                        },
                        'Employee': '',
                        'EmployeeID':'',
                        'Hours': []
                    }
                    
                    self.error_logs.append(error)
                    error_entry_id_counter += 1
                    continue
                else:
                    # If not already processed, insert file ID. We use this for front end.
                    fileIDs.append(file_id)


        ##### ------------------------------------------------------------- #####
        ###                   BEGING PARSING THE SHEET                        ###
        ##### ------------------------------------------------------------- #####


                # get the contractors name
                contractor_name = HumanName(sheet.cell(row=contractor_idx[0], column=contractor_idx[1]).value)
                
                week_ending = sheet.cell(row=week_ending_idx[0], column=week_ending_idx[1]).value
                week_starting = sheet.cell(row=week_ending_idx[0]-1, column=week_ending_idx[1]).value

                week_ending_val = week_ending
                if not isinstance(week_ending, (datetime, date)):
                    week_ending_val = parse_date(week_ending)
                elif isinstance(week_ending, datetime):
                    week_ending_val = week_ending_val.date()

                week_starting_val = week_starting
                if not isinstance(week_starting, (datetime, date)):
                    week_starting_val = parse_date(week_starting)
                elif isinstance(week_starting, datetime):
                    week_starting_val = week_starting_val.date()

                # Check if week ending is correct
                try:
                    if week_ending_val != parse_date(selected_week_ending):
                        error = {
                                'ID': error_entry_id_counter,
                                'FileName': file.filename,
                                "Message":f"This file had a week ending <strong>{week_ending_val}</strong> where selected week ending was {selected_week_ending}. Please select a different week ending and reupload or fix the week ending in the timesheet.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID': contractor_name.full_name,
                                'Hours': []
                        }
                        self.error_logs.append(error)
                        error_entry_id_counter += 1
                        database_service.delete_data_with_fileID(fileID=file_id)
                        continue
                except Exception as e:
                    if week_ending_val != parse_date(selected_week_ending):
                        error = {
                                'ID': error_entry_id_counter,
                                'FileName': file.filename,
                                "Message":f"This file had a week ending <strong>{week_ending_val}</strong> where selected week ending was {selected_week_ending}. Please select a different week ending and reupload or fix the week ending in the timesheet.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID': contractor_name.full_name,
                                'Hours': []
                        }
                        self.error_logs.append(error)
                        error_entry_id_counter += 1
                        database_service.delete_data_with_fileID(fileID=file_id)
                        continue

                # Calculate the expected week starting value
                expected_week_starting_val = week_ending_val - timedelta(days=6)

                # Check if the week starting value is correct
                if not week_starting_val == expected_week_starting_val:
                    error = {
                            'ID': error_entry_id_counter,
                            'FileName': file.filename,
                            "Message":f"This file had a week starting <strong>{week_starting_val}</strong> where expected week starting was {expected_week_starting_val}. Please select a different week ending in Work Order Imports and reupload, or fix the week starting in the timesheet.",
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{
                            },
                            'Employee': '',
                            'EmployeeID': contractor_name.full_name,
                            'Hours': []
                    }
                    self.error_logs.append(error)
                    error_entry_id_counter += 1
                    database_service.delete_data_with_fileID(fileID=file_id)
                    continue

                # Check if the employee filled out their name. If not delete sheet, throw error.
                if contractor_name.full_name.lower().replace(' ', "") == "your name" or contractor_name.full_name.lower().replace(' ', "") == "name":
                    error = {
                            'ID': error_entry_id_counter,
                            'FileName': file.filename,
                            "FIleID": None,
                            "Message":f"These files had the name field not filled out. Expecting name in 'F4'. Please correct this and reupload.",
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{
                            },
                            'Employee': '',
                            'EmployeeID': contractor_name.full_name,
                            'Hours': []
                    }
                    self.error_logs.append(error)
                    error_entry_id_counter += 1
                    database_service.delete_data_with_fileID(fileID=file_id)
                    airtanker_app.logger.debug(f"Error with {file.filename}: file didn't have name field filled out.")
                    continue

                # Get the report job number, the job site, and the week_ending
                reported_project_number = str(sheet.cell(row=job_num[0], column=job_num[1]).value)
                job_site_value = sheet.cell(row=job_site[0], column=job_site[1]).value

                # Check if the week_ending that they put is legit. If not, throw error.
                try:
                    if isinstance(week_ending_val, date):
                        week_ending_str = week_ending_val.strftime("%Y-%m-%d")
                    else: # if it's a string, not datetime - then convert it to a datetime, and then a string in a different variable
                        week_ending = week_ending_val.strptime("%Y-%m-%d") # convert to datetime
                        week_ending_str = week_ending_val.strftime("%Y-%m-%d")
                except:
                    error = {
                        'ID': error_entry_id_counter,
                        'FileName': file.filename,
                        "Message":f"Error with Weekending in this file. User put <strong>{week_ending}</strong>. Please fix, and reupload. It should match the selected date format",
                        'ReportedProjectNumber':'',
                        'WorkOrderEntry':{
                        },
                        'Employee': contractor_name.full_name,
                        "FileID": None,
                        'EmployeeID': contractor_name.full_name,
                        'Hours': []
                    }
                    self.error_logs.append(error)
                    error_entry_id_counter += 1
                    database_service.delete_data_with_fileID(fileID=file_id)
                    airtanker_app.logger.debug(f"Error with {file.filename}: {error['Message']}")
                    continue                

                # Create initial entry for the Data. 
                sheet_data = {
                    "OriginalName": contractor_name.full_name,
                    "ReportedProjectNumber": reported_project_number,
                    "JobSite": job_site_value,
                    "FileID": file_id,
                    "WeekEnding": week_ending_str,
                    "FileName": file.filename,
                    "Data": []
                }

                if 'Trino' in contractor_name.full_name:
                    pass

                # Loop through each row in the sheet
                try: 
                    for row_idx in range(row_start, 30):
                        if str(sheet.cell(row=row_idx, column=2).value).strip().upper() == "TOTALS":
                            break

                        # Grab the data in each row and add it to the Data section of the sheet_data object
                        row_values = {
                            "date": sheet.cell(row=row_idx, column=date_col).value,
                            "account": sheet.cell(row=row_idx, column=account_col).value,
                            "desc": sheet.cell(row=row_idx, column=desc_col).value,
                            "hotel": sheet.cell(row=row_idx, column=hotel_col).value,
                            "transport": sheet.cell(row=row_idx, column=transport_col).value,
                            "per_diem": sheet.cell(row=row_idx, column=per_diem_col).value,
                            "phone": sheet.cell(row=row_idx, column=phone_col).value,
                            "misc": sheet.cell(row=row_idx, column=misc_col).value,
                            "mileage_total": sheet.cell(row=row_idx, column=mileage_col).value
                        }
                        all_none = all(value is None for value in row_values.values())
                        if all_none:
                            continue
                        week_ending_contains_formula = "=" in str(week_ending)
                        for key, value in row_values.items():
                            if value is not None and "=" in str(value) or week_ending_contains_formula:
                                # add error message
                                matching_entry = None
                                if not matching_entry:
                                    for error_entry in self.name_errors:
                                        if error_entry['OriginalName'] == contractor_name.full_name:
                                            matching_entry = error_entry
                                            break  # Stop searching once a match is found
                                if matching_entry:
                                    # remove from name error array
                                    self.name_errors.remove(matching_entry)

                                matching_entry = None
                                for error_entry in self.error_logs:
                                    if error_entry['Employee'] == contractor_name.full_name:
                                        matching_entry = error_entry
                                        break  # Stop searching once a match is found
                                if matching_entry:
                                    # remove from error array
                                    self.error_logs.remove(matching_entry)

                                error = {
                                    'ID': error_entry_id_counter,
                                    'FileName': file.filename,
                                    "Message":f"This file had formulas in cell where actual values were expected. Please correct this and reupload.",
                                    'ReportedProjectNumber':'',
                                    'WorkOrderEntry':{
                                    },
                                    'Employee': contractor_name.full_name,
                                    'EmployeeID': contractor_name.full_name,
                                    'Hours': []
                                }
                                self.error_logs.append(error)
                                error_entry_id_counter += 1
                                database_service.delete_data_with_fileID(fileID=file_id)
                                raise Exception(f"File {file.filename} had formulas in cell where actual values were expected. Please correct this and reupload.")
                            
                        sheet_data["Data"].append(row_values)
                except Exception as e:
                    if "formulas" in e.args[0]:
                        airtanker_app.logger.debug(f"Error with {file.filename}: {e}")
                    continue

                # Now we have all the data from the sheet into the sheet_data object. Now we need to name match and work order match.
                best_match_name, score = process.extractOne(contractor_name.full_name, names_to_match_against)

                ## initialize some variables to store data.
                no_name_found_error = False
                emps_work_orders = []
                project_numbers = []
                work_order_numbers = []

                if score < 90:
                    # if the name doesn't match, check if the name matching is enabled. 
                    name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')
                    if name_matching_enabled == 'true':
                        # Check the DB if name exists in view.
                        query = """
                            SELECT TOP (1000) [FullName]
                                ,[EmpID]
                                ,[TS_Name]
                                ,[NameCount]
                            FROM [dbo].[View_NameSelectionCounts]
                            WHERE TS_NAME = ?
                            ORDER by NameCount
                        """
                        results = database_service.execute_query(query, contractor_name.full_name)
                        if results:
                            best_match_name = results[0]["FullName"]
                            score = 100

                    # If name matching is not enabled it'll take us here immediately.
                    if score < 90:

                        # throw an exception, but add the sheet data to the error.
                        # this method will check to see if all 5 closed matches even have timesheets uploaded.
                        no_name_found_error, name_error = expense_name_error(all_active_work_order_entries,
                                                                        contractor_name,
                                                                        names_to_match_against,
                                                                        job_site_value,
                                                                        error_entry_id_counter,
                                                                        file.filename,
                                                                        week_ending_val,
                                                                        file_id,
                                                                        database_service)
                        if name_error:
                            # this means a timesheet does exist for the matches. Append the sheet data to the errors now.
                            name_error["Data"] = sheet_data
                            self.name_errors.append(name_error)
                            error_entry_id_counter += 1
                            continue # skip this file, continue to next file. We have the data and the associated work orders with each close match.
                        else:
                            # this means all 5 closest matches don't have timesheets uploaded.
                            error = {
                                'ID': error_entry_id_counter,
                                'FileName': file.filename,
                                "Message":f"Couldn't find a match for {contractor_name.full_name} in the database. And all 5 closest matches didn't have hours.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{
                                },
                                'Employee': '',
                                'EmployeeID': contractor_name.full_name,
                                'Hours': []
                            }
                            self.error_logs.append(error)
                            error_entry_id_counter += 1
                            database_service.delete_data_with_fileID(fileID=file_id)
                            airtanker_app.logger.debug(f"Error with {file.filename}: Couldn't find a match for {contractor_name.full_name} in the database. And all 5 closest matches didn't have hours.")
                            continue

                # if the name matches close enough
                if score > 89:

                    # loop through all the active work orders where the name associated with work order matches the best match
                    for wo_entry in all_active_work_order_entries:
                        if wo_entry["FullName"] == best_match_name:
                            emps_work_orders.append(wo_entry)
                            
                            curr_project_number = wo_entry['ProjectNumber'].strip()
                            project_numbers.append(curr_project_number)

                            curr_work_order_number = wo_entry["WorkOrderNumber"].strip()
                            work_order_numbers.append(curr_work_order_number)

                    curr_employee_id = emps_work_orders[0]['EmployeeID'] # get the employeeID from it.

                # Now, we have all the data from the sheet, and the correct employeee association.

                try:
                    # if they've actually put down a work order number in their file
                    if reported_project_number:
                        # Check if they have any timesheets available first. If not, then it doesn't matter what they put.
                        timesheet_query = """
                                SELECT  [Source]
                                        ,[TimesheetID]
                                        ,[WeekEnding]
                                        ,[FileName]
                                        ,[TimeSheetEntryID]
                                        ,[FirstName]
                                        ,[LastName]
                                        ,[EmployeeID]
                                        ,[Date]
                                        ,[ReportedHours]
                                        ,[WorkOrderNumber]
                                        ,[WorkOrderID]
                                        ,[SiteSheetID]
                                        ,[ProjectNumber]
                                        ,[TaskName]
                                FROM [dbo].[vw_EmployeeDetailsAllUnion]
                                WHERE EmployeeID = ? AND WeekEnding = ? AND Source IN ('Internal', 'Paycor', 'ADP')
                            """
                        parameters = (curr_employee_id, week_ending)

                        timesheet_results = database_service.execute_query(timesheet_query, parameters)
                        if not timesheet_results:
                            # THROW ERROR. User needs to upload internal Timesheet first!
                            error = {
                                'ID': error_entry_id_counter,
                                'FileName': file.filename,
                                "Message":f"Database doesn't contain timesheet hours for these employees! Please upload their internal timesheets first.",
                                'ReportedProjectNumber':'',
                                'WorkOrderEntry':{},
                                'Employee':"",
                                'EmployeeID': contractor_name.full_name,
                                'Hours': [
                                    {
                                        'Date':week_ending_str,
                                        'FileID':file_id,
                                        'Hours':"",
                                        'TaskID':""
                                    }
                                ]
                            }
                            self.error_logs.append(error)
                            error_entry_id_counter += 1
                            database_service.delete_data_with_fileID(fileID=file_id)
                            raise Exception(f"Couldn't find timesheets for weekending {week_ending_str}, and contractors: {contractor_name.full_name}.")
                        else:
                            # now we can compare the work order result since they do in fact have timesheets uploaded
                            project_number_mismatch = False
                            best_match_wo_number, wo_score = process.extractOne(str(reported_project_number), work_order_numbers) # Check which WO it is, if multiple.
                            best_match_project_number, score = process.extractOne(str(reported_project_number), project_numbers) # Check which WO it is, if multiple.

                            # check if the project matches what they put down on the file
                            if score < 90 and wo_score < 90:
                                # now see if the timesheets only has one

                                # Aggregate the WorkOrderIDs from the timesheets
                                work_order_numbers = []
                                for timesheet in timesheet_results:
                                    if timesheet["WorkOrderNumber"] not in work_order_numbers:
                                        work_order_numbers.append(timesheet["WorkOrderNumber"])

                                # if only one work order exists for that week, just use that.
                                if len(work_order_numbers) == 1:
                                    best_match_wo_number = work_order_numbers[0]
                                else:
                                    # else, project has mismatch. User must choose which one.
                                    project_number_mismatch = True

                            if project_number_mismatch:
                                ## append errors. User needs to select the correct Project / Work Order
                                error = expense_standard_error(emps_work_orders,
                                                error_entry_id_counter,
                                                file.filename,
                                                reported_project_number if isinstance(reported_project_number, str) else 0,
                                                contractor_name.full_name,
                                                curr_employee_id,
                                                sheet_data["Data"],
                                                week_ending_val,
                                                job_site_value,
                                                file_id,
                                                self.error_logs)
                                if error:
                                    self.error_logs.append(error)
                                    error_entry_id_counter += 1
                            else:
                                selected_work_order_array = [wo for wo in emps_work_orders if wo.get("WorkOrderNumber").strip() == best_match_wo_number]
                                selected_work_order = None
                                if selected_work_order_array: ## I added the NOT part, remove if uncommenting below
                                    selected_work_order = selected_work_order_array[0]
                                else:
                                    selected_work_order_array = [wo for wo in emps_work_orders if wo.get("ProjectNumber").strip() == best_match_project_number]
                                    selected_work_order = selected_work_order_array[0]
                                
                                if selected_work_order.get("SiteSheetID"):
                                    # Check each day based on the reported data

                                    # Need to add AirFare 

                                    sql_insert = """
                                        INSERT INTO [dbo].[ReportedExpenses]
                                            ([ExpenseDate]
                                            ,[Account]
                                            ,[Description]
                                            ,[Lodging]
                                            ,[RentalCar]
                                            ,[PerDiem]
                                            ,[Phone]
                                            ,[Misc]
                                            ,[MileageTotal]
                                            ,[FileID]
                                            ,[TimesheetEntryID]
                                            ,[EmployeeID]
                                            ,[WorkOrderID]
                                            ,[TimesheetID]
                                            ,[JobSite]
                                            ,[SiteSheetID])
                                        VALUES
                                            (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                    """
                                    jobsite = sheet_data["JobSite"]
                                    for expense_row in sheet_data["Data"]:

                                        # skip rows that don't have a date.
                                        if expense_row["date"] == None:
                                            continue

                                        # skip rows that don't have expenses.
                                        if is_empty_expense(expense_row):
                                            continue

                                        timesheetEntryID = database_service.find_or_create_timesheetEntry(timesheet_results[0]["TimesheetID"], date=expense_row["date"])
                                        values = tuple(expense_row.values())
                                        values += (file_id,
                                                    timesheetEntryID,
                                                    curr_employee_id,
                                                    selected_work_order["WorkOrderID"],
                                                    timesheet_results[0]["TimesheetID"],
                                                    jobsite,
                                                    selected_work_order["SiteSheetID"])
                                        database_service.execute_query(sql_insert, parameters=values)

                                    query = """
                                        UPDATE [dbo].[Timesheets]
                                        SET [ExpensesUploaded] = 1,
                                            [Expense_StatusID] = 7
                                        WHERE TimesheetID = ?
                                    """
                                    database_service.execute_query(query, (timesheet_results[0]["TimesheetID"]))
                                else:
                                    error = {
                                        'ID': error_entry_id_counter,
                                        'FileName': file.filename,
                                        "Message":f"WorkOrder doesn't contain a SiteSheetID. Please add this field to the workorder in Odoo, and upload this expense report again {selected_work_order['WorkOrderNumber']}",
                                        'ReportedProjectNumber':'',
                                        'WorkOrderEntry':{},
                                        'Employee': contractor_name.full_name,
                                        'EmployeeID': contractor_name.full_name,
                                        'Hours': [
                                            {
                                                'Date':week_ending_str,
                                                'FileID':file_id,
                                                'Hours':"",
                                                'TaskID':""
                                            }
                                        ]
                                    }
                                    self.error_logs.append(error)
                                    error_entry_id_counter += 1
                                    database_service.delete_data_with_fileID(fileID=file_id)
                                    raise Exception(f"WorkOrder doesn't contain a SiteSheetID. Please add this field to the workorder in Odoo, and upload this expense report again {selected_work_order['WorkOrderNumber']}")
                    else:
                        # no project number found in sheet.
                        error = {
                            'ID': error_entry_id_counter,
                            'FileName': file.filename,
                            "Message":f"No project number could be found on this expenses report. Please add this field to the excel file.",
                            'ReportedProjectNumber':'',
                            'WorkOrderEntry':{},
                            'Employee': contractor_name.full_name,
                            'EmployeeID': contractor_name.full_name,
                            'Hours': [
                                {
                                    'Date':week_ending_str,
                                    'FileID':file_id,
                                    'Hours':"",
                                    'TaskID':""
                                }
                            ]
                        }
                        self.error_logs.append(error)
                        error_entry_id_counter += 1
                        database_service.delete_data_with_fileID(fileID=file_id)
                        raise Exception(f"Couldn't find timesheets for weekending {week_ending_str}, and contractors: {contractor_name.full_name}.")
                except Exception as e:
                    airtanker_app.logger.debug(f"Error with {file.filename}: {e}")
                    continue

        database_service.disconnect()
        return self.error_logs, self.name_errors, fileIDs

def check_pdf_tables_and_format(temp_pdf_path, start_page=1, max_page=3):
    """
    Iterates through PDF pages (from start_page to a maximum of max_page or total pages) to detect the format and find
    a table with expected headers. Returns the table DataFrame, a detected format ('HTI', 'Valiant', or 'Unknown'),
    the page number (as a string) and an error message if its the case.

    Returns: df, pdf_customer_format, page_number, error_message
    """
    # Determine total number of pages in the PDF.
    try:
        with open(temp_pdf_path, 'rb') as pdf_file:
            reader = PdfReader(pdf_file)
            total_pages = len(reader.pages)
    except Exception as e:
        # airtanker_app.logger.error(f"Failed to open PDF: {e}")
        return None, 'Unknown', None, "Failed to open PDF"

    if total_pages == 0:
        # airtanker_app.logger.error("PDF has no pages.")
        return None, 'Unknown', None, "PDF has no pages"

    # Limit the maximum page to check based on the total pages and the max_page value.
    max_page = min(max_page, total_pages)

    df = None
    detected_format = 'Unknown'

    expected_hti_headers = ['Employee Name', 'Work Date', 'Work Day', 'Type','Rate', 'Cost', 'Hours', 'Total']
    expected_valiant_headers = ['Employee Name', 'Number', 'TRAVEL', 'RT', 'OT', 'DT']
    expected_dominion_headers = ['Date', 'Facility', 'Work Center', 'Equipment', 'Job Number', 'Time in', 'Lunch Hrs.', 'Time Out', 'Total Hrs.']
    image_based_possible_customer_names = ['Fori', 'Fori Automation'] # List of possible customer names to search in the image-based pages ocr

    # Track which pages were processed and which were images
    image_pages = set()
    processed_pages = 0

    # Iterate through pages starting at 'start_page'
    for page in range(start_page, max_page + 1):
        page_str = str(page)
        processed_pages += 1

        try:
            tables = camelot.read_pdf(
                temp_pdf_path,
                pages=page_str,
                flavor='stream',
                multiple_tables=False
            )
            if len(tables) == 0:
                airtanker_app.logger.warning(f"No tables found on page {page_str} - might be an image-based page")
                image_pages.add(page)
                continue

        except Exception as e:
            # log the error and continue with the next page.
            airtanker_app.logger.error(f"Error reading page {page_str}: {e}")
            image_pages.add(page) # We add the page to the image_pages set because we don't know if it's an image-based page or not.
            continue

        df = tables[0].df
        
        # Check for HTI format.
        for _, row in df.iterrows():
            if is_header_row(row, expected_hti_headers):
                detected_format = "HTI"
                return df, detected_format, page_str, None

        # Check for Valiant format.
        for _, row in df.iterrows():
            if is_header_row(row, expected_valiant_headers):
                detected_format = "Valiant"
                return df, detected_format, page_str, None

        # Check for Dominion format.
        for _, row in df.iterrows():
            if is_header_row(row, expected_dominion_headers):
                detected_format = "Dominion"
                return df, detected_format, page_str, None
        
        # If a table was found but no header matched, continue to the next page.
        airtanker_app.logger.warning(f"Table found on page {page_str} did not match any expected format.")

        # Print table rows for debugging
        # for index, row in df.iterrows():
        #     airtanker_app.logger.info(f"Row {index}: {row}")

    # Check if all processed pages were images
    # If so, parse them via OCR to check for possible customer names and find tables
    if len(image_pages) == processed_pages:
        error_msg = f"All {processed_pages} pages appear to be image-based."
        # airtanker_app.logger.error(error_msg)
        return None, 'Unknown', None, error_msg

    # No matching table was found.
    return df, detected_format, str(page), "No matching table was found"

def is_empty_expense(expense):
    
    keys_to_check = ['hotel', 'transport', 'per_diem', 'phone', 'misc', 'mileage_total']

    for key in keys_to_check:
        value = expense.get(key)
        if value is not None:
            return False
    return True

def is_header_row(row, expected_headers, threshold=0.5):
    """
    Check if the given row is likely to be a header row based on the presence of expected headers.
    """
    match_count = sum(1 for cell in row if any(header.lower() in str(cell).lower() for header in expected_headers))
    return match_count / len(expected_headers) >= threshold

def add_hours(timesheet_record, hour_type, hours):
    if hour_type.replace(" ", "").lower() == "overtime":
        if timesheet_record.internal_overtime_hours == '':
             timesheet_record.internal_overtime_hours = hours
        else:
            timesheet_record.internal_overtime_hours = round(float(timesheet_record.internal_overtime_hours) + float(hours), 2)        

    elif hour_type.replace(" ", "").lower() == "doubletime":
        if timesheet_record.internal_doubletime_hours == '':
            timesheet_record.internal_doubletime_hours = hours
        else:
            timesheet_record.internal_doubletime_hours = round(float(timesheet_record.internal_doubletime_hours) + float(hours), 2)        
    else:
        if timesheet_record.internal_regular_hours == '':
            timesheet_record.internal_regular_hours = hours
        else:
            timesheet_record.internal_regular_hours = round(float(timesheet_record.internal_regular_hours) + float(hours), 2)        

    timesheet_record.notes += f"Added {hours} to {hour_type}. "

def get_customer_from_work_order(work_order):
    pattern = r'^([^ -]+)-([^-]+)-([^-]+)'
    match = re.match(pattern, work_order)
    if match:
        job_identifier = match.group(1)
        customer = match.group(2)
        machine_number = match.group(3)
    else:
        customer = "Unknown."
    return customer

def find_timerecord_by_daterange(objects, employee_name, job_name, date_range):
    for obj in objects:
        if obj.employee_name == employee_name and obj.job_name == job_name and obj.date_range == date_range:
            return obj
    return None

def find_timerecord_by_customer(objects, employee_name, job_name):
    for obj in objects:
        if obj.employee_name == employee_name and obj.job_name == job_name:
            return obj
    return None

def lookahead(iterable):
    """Provide a lookahead functionality for an iterator."""
    # Duplicate the original iterator
    first, second = tee(iterable)
    # Chain the second iterator with a sentinel value at the end
    second_advanced = chain(islice(second, 1, None), [None])
    # Zip them together, so we get a (current, next) pair
    return zip(first, second_advanced)

def expense_name_error(all_active_work_order_entries,
                   employee_name,
                   names_to_match_against,
                   job_site,
                   error_entry_id_counter,
                   file_name,
                   week_ending,
                   file_id,
                   database_service # type: DatabaseService
                   ):
        
        if isinstance(week_ending, date):
            week_ending_str = week_ending.strftime("%Y-%m-%d")
        else: # if it's a string, not datetime - then convert it to a datetime, and then a string in a different variable
            week_ending = week_ending.strptime("%Y-%m-%d")
            week_ending_str = week_ending.strftime("%Y-%m-%d")

        # Put into errors
        name_error = None
        matches = process.extract(employee_name.full_name, names_to_match_against, limit=5)
        timesheet_counter = 0

        for match in matches:
            match_name, score = match
            # Get the hours and get the work orders of each
            emps_work_orders = []
            project_numbers = []
            for wo_entry in all_active_work_order_entries:
                if wo_entry["FullName"] == match_name:
                    emps_work_orders.append(wo_entry)
                    curr_project_number = wo_entry['ProjectNumber'].strip()
                    project_numbers.append(curr_project_number)

            work_order_entry = {}
            employee_id = emps_work_orders[0]["EmployeeID"]
            emps_work_order_ids = [wo["WorkOrderID"] for wo in emps_work_orders]

            # Create a string of placeholders for the SQL query
            placeholders = ', '.join(['?'] * len(emps_work_order_ids))
            
            timesheet_query = f"""
                SELECT  [Source]
                        ,[TimesheetID]
                        ,[WeekEnding]
                        ,[FileName]
                        ,[TimeSheetEntryID]
                        ,[FirstName]
                        ,[LastName]
                        ,[EmployeeID]
                        ,[Date]
                        ,[ReportedHours]
                        ,[WorkOrderNumber]
                        ,[WorkOrderID]
                        ,[SiteSheetID]
                        ,[ProjectNumber]
                        ,[TaskName]
                FROM [dbo].[vw_EmployeeDetailsAllUnion]
                WHERE EmployeeID = ? AND WeekEnding = ? AND WorkOrderID in ({placeholders}) AND Source IN ('Internal', 'ADP', 'Paycor') 
            """
            parameters = [employee_id, week_ending] + emps_work_order_ids
            results = database_service.execute_query(timesheet_query, parameters)
            if not results:
                timesheet_counter += 1
                continue

            for entry in emps_work_orders:
                if entry['ProjectNumber'].strip() in work_order_entry:
                    # append it
                    work_order_entry[entry['ProjectNumber'].strip()].append({
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                        })
                else:
                    work_order_entry[entry["ProjectNumber"].strip()] = [{
                        entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                    }]
            
            if name_error:     
                data = {
                    'EmployeeName':match_name,
                    'EmployeeID':employee_id,
                    'WorkOrders':work_order_entry
                }
                name_error['EmployeeData'].append(data)
            else:
                name_error = {
                    'ID':error_entry_id_counter,
                    'OriginalName':employee_name.full_name,
                    'FileName': file_name,
                    "FileID": file_id,
                    "WeekEnding":week_ending_str,
                    'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                    'EmployeeData':[{
                        'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                    }],
                    "JobSite": job_site,
                    'Data':[]
                }

        if timesheet_counter >= 5:
            return False, None # This means no matches had timesheet records.
        return True, name_error # no_name_found_error, error_not_found_low_score, name_error / new entry 

def expense_standard_error(emps_work_orders,
                       error_entry_id_counter,
                       file_name,
                       reported_project_number,
                       employee_name,
                       curr_employee_id,
                       row_values,
                       week_ending,
                       job_site,                   
                       file_id,
                       errors):
    
    if isinstance(week_ending, date):
        week_ending_str = week_ending.strftime("%Y-%m-%d")
    else: # if it's a string, not datetime - then convert it to a datetime, and then a string in a different variable
        week_ending = week_ending.strptime("%Y-%m-%d")
        week_ending_str = week_ending.strftime("%Y-%m-%d")
        
    work_order_entry = {
    }
    for entry in emps_work_orders:
        if entry['ProjectNumber'].strip() in work_order_entry:
            # append it
            work_order_entry[entry['ProjectNumber'].strip()].append({
                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                })
        else:
            work_order_entry[entry["ProjectNumber"].strip()] = [{
                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
            }]

    message = f"{employee_name} Reported Project Number: '{reported_project_number}' isn't found in any assigned work orders."
    if reported_project_number == 0:
        message = f"{employee_name} didn't report a Job Number. Select correct one or skip update."

    error = {
        'ID':error_entry_id_counter,
        'FileName': file_name,
        'FileID': file_id,
        "WeekEnding": week_ending_str,
        "JobSite": job_site,
        'Message': message,
        'ReportedProjectNumber':reported_project_number,
        'WorkOrderEntry':work_order_entry,
        'Employee': employee_name,
        'EmployeeID':curr_employee_id,
        'Data': row_values
    }

    # Iterate over the list of errors to find the match
    matching_entry = None
    for error_entry in errors:
        if error_entry['EmployeeID'] == curr_employee_id and error_entry['ReportedProjectNumber'] == reported_project_number:
            matching_entry = error_entry
            break  # Stop searching once a match is found
    if matching_entry:
        # If found, add to the current hours
        hours_match = None
        matching_entry["Data"].append(row_values)
        return None
    else:
        return error